import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Typography system for the Upshift app
/// Using Inter font family for modern, readable design
class AppTypography {
  // Base font family
  static String get fontFamily => GoogleFonts.inter().fontFamily ?? 'Inter';

  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;

  // Line heights (as multipliers)
  static const double tightLineHeight = 1.1;
  static const double normalLineHeight = 1.4;
  static const double relaxedLineHeight = 1.6;

  /// Complete text theme following Material Design 3 guidelines
  static TextTheme get textTheme {
    return TextTheme(
      // Display styles - for hero sections and major headings
      displayLarge: GoogleFonts.inter(
        fontSize: 57,
        fontWeight: bold,
        height: tightLineHeight,
        letterSpacing: -0.25,
      ),
      displayMedium: GoogleFonts.inter(
        fontSize: 45,
        fontWeight: bold,
        height: tightLineHeight,
        letterSpacing: 0,
      ),
      displaySmall: GoogleFonts.inter(
        fontSize: 36,
        fontWeight: bold,
        height: tightLineHeight,
        letterSpacing: 0,
      ),

      // Headline styles - for page titles and section headers
      headlineLarge: GoogleFonts.inter(
        fontSize: 32,
        fontWeight: semiBold,
        height: normalLineHeight,
        letterSpacing: 0,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: 28,
        fontWeight: semiBold,
        height: normalLineHeight,
        letterSpacing: 0,
      ),
      headlineSmall: GoogleFonts.inter(
        fontSize: 24,
        fontWeight: semiBold,
        height: normalLineHeight,
        letterSpacing: 0,
      ),

      // Title styles - for card titles and important content
      titleLarge: GoogleFonts.inter(
        fontSize: 22,
        fontWeight: medium,
        height: normalLineHeight,
        letterSpacing: 0,
      ),
      titleMedium: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: medium,
        height: normalLineHeight,
        letterSpacing: 0.15,
      ),
      titleSmall: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: medium,
        height: normalLineHeight,
        letterSpacing: 0.1,
      ),

      // Body styles - for main content
      bodyLarge: GoogleFonts.inter(
        fontSize: 16,
        fontWeight: regular,
        height: relaxedLineHeight,
        letterSpacing: 0.5,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: regular,
        height: relaxedLineHeight,
        letterSpacing: 0.25,
      ),
      bodySmall: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: regular,
        height: normalLineHeight,
        letterSpacing: 0.4,
      ),

      // Label styles - for buttons, form labels, and captions
      labelLarge: GoogleFonts.inter(
        fontSize: 14,
        fontWeight: medium,
        height: normalLineHeight,
        letterSpacing: 0.1,
      ),
      labelMedium: GoogleFonts.inter(
        fontSize: 12,
        fontWeight: medium,
        height: normalLineHeight,
        letterSpacing: 0.5,
      ),
      labelSmall: GoogleFonts.inter(
        fontSize: 11,
        fontWeight: medium,
        height: normalLineHeight,
        letterSpacing: 0.5,
      ),
    );
  }

  // Custom text styles for specific use cases

  /// Chat message text style
  static TextStyle get chatMessage => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: regular,
    height: relaxedLineHeight,
    letterSpacing: 0.25,
  );

  /// Chat timestamp text style
  static TextStyle get chatTimestamp => GoogleFonts.inter(
    fontSize: 11,
    fontWeight: regular,
    height: normalLineHeight,
    letterSpacing: 0.4,
  );

  /// Progress indicator text style
  static TextStyle get progressText => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: semiBold,
    height: normalLineHeight,
    letterSpacing: 0.1,
  );

  /// Achievement badge text style
  static TextStyle get achievementBadge => GoogleFonts.inter(
    fontSize: 12,
    fontWeight: bold,
    height: normalLineHeight,
    letterSpacing: 0.5,
  );

  /// Category tag text style
  static TextStyle get categoryTag => GoogleFonts.inter(
    fontSize: 11,
    fontWeight: semiBold,
    height: normalLineHeight,
    letterSpacing: 0.5,
  );

  /// Step number text style
  static TextStyle get stepNumber => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: bold,
    height: normalLineHeight,
    letterSpacing: 0,
  );

  /// Onboarding title text style
  static TextStyle get onboardingTitle => GoogleFonts.inter(
    fontSize: 28,
    fontWeight: bold,
    height: tightLineHeight,
    letterSpacing: -0.5,
  );

  /// Onboarding subtitle text style
  static TextStyle get onboardingSubtitle => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: regular,
    height: relaxedLineHeight,
    letterSpacing: 0.25,
  );

  /// Button text styles
  static TextStyle get primaryButton => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: semiBold,
    height: normalLineHeight,
    letterSpacing: 0.1,
  );

  static TextStyle get secondaryButton => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: medium,
    height: normalLineHeight,
    letterSpacing: 0.25,
  );

  /// Navigation text styles
  static TextStyle get bottomNavSelected => GoogleFonts.inter(
    fontSize: 12,
    fontWeight: semiBold,
    height: normalLineHeight,
    letterSpacing: 0.5,
  );

  static TextStyle get bottomNavUnselected => GoogleFonts.inter(
    fontSize: 12,
    fontWeight: regular,
    height: normalLineHeight,
    letterSpacing: 0.5,
  );

  /// Form text styles
  static TextStyle get inputLabel => GoogleFonts.inter(
    fontSize: 14,
    fontWeight: medium,
    height: normalLineHeight,
    letterSpacing: 0.25,
  );

  static TextStyle get inputText => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: regular,
    height: normalLineHeight,
    letterSpacing: 0.15,
  );

  static TextStyle get inputHint => GoogleFonts.inter(
    fontSize: 16,
    fontWeight: regular,
    height: normalLineHeight,
    letterSpacing: 0.15,
  );

  static TextStyle get helperText => GoogleFonts.inter(
    fontSize: 12,
    fontWeight: regular,
    height: normalLineHeight,
    letterSpacing: 0.4,
  );

  static TextStyle get errorText => GoogleFonts.inter(
    fontSize: 12,
    fontWeight: medium,
    height: normalLineHeight,
    letterSpacing: 0.4,
  );

  /// Utility methods for text style modifications

  /// Apply color to text style
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  /// Apply opacity to text style
  static TextStyle withOpacity(TextStyle style, double opacity) {
    return style.copyWith(color: style.color?.withValues(alpha: opacity));
  }

  /// Apply weight to text style
  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }

  /// Apply size to text style
  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size);
  }

  /// Apply letter spacing to text style
  static TextStyle withLetterSpacing(TextStyle style, double letterSpacing) {
    return style.copyWith(letterSpacing: letterSpacing);
  }

  /// Apply line height to text style
  static TextStyle withLineHeight(TextStyle style, double height) {
    return style.copyWith(height: height);
  }

  /// Create responsive text style based on screen size
  static TextStyle responsive(TextStyle baseStyle, BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 360) {
      // Small screens - reduce font size slightly
      return baseStyle.copyWith(fontSize: (baseStyle.fontSize ?? 14) * 0.9);
    } else if (screenWidth > 600) {
      // Large screens - increase font size slightly
      return baseStyle.copyWith(fontSize: (baseStyle.fontSize ?? 14) * 1.1);
    }

    return baseStyle;
  }
}
